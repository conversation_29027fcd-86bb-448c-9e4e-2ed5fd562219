package com.everpoets.urlshortener

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.everpoets.urlshortener.ui.theme.UrlShortenerTheme
import com.everpoets.urlshortener.view.components.HomeScreen
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeActivity : ComponentActivity() {
    private val mainViewModel: MainViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            UrlShortenerTheme {
                val uiState by mainViewModel.shortenUiState.collectAsState()

                HomeScreen(
                    uiState = uiState,
                    dispatchAction = {
                        mainViewModel.dispatchAction(it)
                    }
                )
            }
        }
    }
}
