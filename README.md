# URL Shortener Android App

A modern Android application built with Jetpack Compose that allows users to shorten URLs using a remote API service. The app features a clean, intuitive interface and follows modern Android development best practices.

## 📸 Screenshots

<img src="apk_screenshot.png" alt="URL Shortener App Screenshot" width="300"/>

## 📱 Features

- **URL Shortening**: Convert long URLs into short, shareable links
- **History Management**: View and manage previously shortened URLs
- **Duplicate Detection**: Automatically detects and handles duplicate URLs
- **Modern UI**: Built with Jetpack Compose for a smooth user experience
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Offline Support**: Graceful handling of network connectivity issues

## 🏗️ Architecture

The app follows **MVI (Model-View-Intent)** architecture pattern with:

- **Clean Architecture**: Separation of concerns with distinct layers
- **Repository Pattern**: Abstraction layer for data operations
- **Dependency Injection**: Using Dagger Hilt for dependency management
- **Reactive Programming**: StateFlow for reactive UI updates
- **Single Activity Architecture**: Using Jetpack Compose

### Architecture Decision: Simplified Layer Structure

This project intentionally **does not include Domain and UseCase layers** for the following reasons:

- **Project Simplicity**: The app has a single main feature (URL shortening) with minimal business logic
- **Avoid Over-Engineering**: Adding Domain/UseCase layers would introduce unnecessary complexity for this scope
- **Direct Repository Access**: ViewModels directly access repositories, which is sufficient for simple CRUD operations
- **Maintainability**: Fewer layers mean easier maintenance and faster development for small projects
- **YAGNI Principle**: "You Aren't Gonna Need It" - additional layers can be added later if business logic complexity grows

**When to add Domain/UseCase layers:**
- Multiple complex business rules
- Cross-feature data manipulation
- Complex validation logic
- Multiple data sources requiring coordination
- Team size and project complexity justify the additional abstraction

## 🛠️ Tech Stack

### Core Technologies
- **Kotlin**: Primary programming language (v2.0.21)
- **Jetpack Compose**: Modern UI toolkit (BOM 2024.09.00)
- **Android SDK**: Target SDK 36, Min SDK 24

### Architecture Components
- **ViewModel**: UI-related data holder
- **StateFlow**: Reactive state management
- **Lifecycle Runtime KTX**: Lifecycle-aware components (v2.6.1)
- **Activity Compose**: Integration between Activity and Compose (v1.8.0)

### UI & Design
- **Material 3**: Material Design 3 components
- **Compose UI**: Core Compose UI components

### Networking
- **Retrofit**: HTTP client for API calls (v3.0.0)
- **Kotlinx Serialization**: JSON serialization/deserialization (v1.6.3)
- **Kotlinx Serialization Converter**: Retrofit converter for Kotlinx Serialization
- **OkHttp**: HTTP client with connection pooling (included with Retrofit)

### Dependency Injection
- **Dagger Hilt**: Dependency injection framework (v2.56.2)
- **Hilt Android Compiler**: Annotation processor for Hilt

### Coroutines
- **Kotlinx Coroutines**: Asynchronous programming support

### Testing
- **JUnit**: Unit testing framework (v4.13.2)
- **AndroidX JUnit**: Android-specific JUnit extensions (v1.1.5)
- **MockK**: Mocking library for Kotlin (v1.13.5)
- **Compose UI Test JUnit4**: Testing utilities for Compose UI
- **Kotlinx Coroutines Test**: Testing utilities for coroutines (v1.7.3)
- **Hilt Android Testing**: Testing utilities for Hilt

### Development Tools
- **Compose UI Tooling**: Debug tools for Compose (debugImplementation)
- **Compose UI Test Manifest**: Test manifest for Compose (debugImplementation)

### Code Quality
- **Detekt**: Static code analysis (v1.23.8)
- **Detekt Compose**: Compose-specific static analysis rules (v0.4.22)
- **KSP**: Kotlin Symbol Processing (v2.0.21-1.0.27)

## 📦 Project Structure

```
app/
├── src/
│   ├── main/
│   │   ├── java/com/everpoets/urlshortener/
│   │   │   ├── data/                    # Data layer
│   │   │   │   ├── remote/              # API service and DTOs
│   │   │   │   ├── ShortenUrlModel.kt   # Domain model
│   │   │   │   └── ShortenUrlRepository.kt
│   │   │   ├── di/                      # Dependency injection modules
│   │   │   ├── toolkit/                 # Utility classes and extensions
│   │   │   │   └── network/             # Network utilities
│   │   │   ├── ui/theme/                # UI theming
│   │   │   ├── view/                    # UI layer
│   │   │   │   ├── components/          # Reusable UI components
│   │   │   │   └── HomeAction.kt        # UI actions
│   │   │   ├── App.kt                   # Application class
│   │   │   ├── HomeActivity.kt          # Main activity
│   │   │   └── HomeViewModel.kt         # Main view model
│   │   └── res/                         # Resources
│   ├── test/                           # Unit tests
│   └── androidTest/                    # Instrumentation tests
├── build.gradle.kts                   # Module build configuration
└── proguard-rules.pro                 # ProGuard configuration
```

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- JDK 11 or later
- Android SDK with API level 33

### Installation

2. **Open in Android Studio**
   - Launch Android Studio
   - Select "Open an existing project"
   - Navigate to the cloned directory and select it

3. **Build the project**
   ```bash
   ./gradlew build
   ```

4. **Run the app**
   - Connect an Android device or start an emulator
   - Click the "Run" button in Android Studio or use:
   ```bash
   ./gradlew installDebug
   ```

## 🔧 Configuration

### API Configuration
The app uses a remote URL shortening service. The base URL is configured in:
```kotlin
// app/src/main/java/com/everpoets/urlshortener/di/ApiModule.kt
private const val BASE_URL = "https://url-shortener-server.onrender.com/api/"
```

### Network Timeout
Network timeouts can be configured in the `ApiModule`:
```kotlin
.callTimeout(5, TimeUnit.SECONDS)
```

### Network Error Handling
The app includes custom network utilities in the `toolkit.network` package:

- **SafeApiCall Extension**: Wraps API calls with proper error handling
- **NoInternetException**: Custom exception for connection failures
- **Automatic Retry**: OkHttp configured with connection failure retry

```kotlin
// Usage example
val result = safeApiCall {
    apiService.shortenUrl(request)
}
```

## 🧪 Testing

### Running Unit Tests
```bash
./gradlew test
```

### Running Instrumentation Tests
```bash
./gradlew connectedAndroidTest
```

### Running All Tests
```bash
./gradlew check
```


### Test Utilities
The project includes custom test utilities:

- **MainDispatcherRule**: JUnit rule for testing coroutines with proper dispatcher setup
- **MockK Integration**: Comprehensive mocking for unit tests
- **Compose Testing**: UI testing utilities for Compose components

**Test Structure:**
- `test/` - Unit tests for ViewModels, Repositories, and utilities
- `androidTest/` - Instrumentation tests for UI components
- `utils/` - Shared test utilities and rules

## 📊 Code Quality

### Static Analysis with Detekt
The project uses Detekt for static code analysis with additional Compose-specific rules:

```bash
# Run standard Detekt analysis
./gradlew detekt

# Run Detekt and continue on failure (to see all issues)
./gradlew detekt --continue
```

**Detekt Configuration:**
- **Standard Rules**: Kotlin code quality and style checks
- **Compose Rules**: Jetpack Compose best practices and performance optimizations
- **Custom Configuration**: Located in `config/detekt/detekt.yml`

### Lint Checks
```bash
./gradlew lint
```
## 🎨 UI Components

### Main Components
- **HomeScreen**: Main screen container with Scaffold and Snackbar
- **InputTextButtonComponent**: Text input with send button for URL entry
- **ShortenUrlListComponent**: Displays list of shortened URLs with LazyColumn
- **Custom Theme**: Material 3 design system implementation

## 🔄 State Management (MVI Pattern)

The app implements the **MVI (Model-View-Intent)** pattern with unidirectional data flow:

### Intent (Actions)
User intentions are represented as sealed interface actions:
```kotlin
sealed interface HomeAction {
    data class ShortenUrl(val url: String) : HomeAction
    data object ClearUserMessage : HomeAction
}
```

### Model (State)
The UI state is represented as a single immutable data class:
```kotlin
data class ShortenUiState(
    val isLoading: Boolean = false,
    val list: List<ShortenUrlModel> = emptyList(),
    @StringRes val userMessage: Int? = null
)
```

### View
The Compose UI observes the state and dispatches actions:
```kotlin
val uiState by mainViewModel.shortenUiState.collectAsState()
// UI reacts to state changes and dispatches actions
mainViewModel.dispatchAction(HomeAction.ShortenUrl(url))
```

### Data Flow
1. **Intent**: User interactions create actions
2. **Model**: Actions are processed to update state
3. **View**: UI recomposes based on state changes

## 🌐 API Integration

### Endpoint
- **POST** `/alias` - Shorten a URL

### Request Format
```json
{
  "url": "https://example.com"
}
```

### Response Format
```json
{
  "alias": "abc123",
  "_links": {
    "self": "https://example.com",
    "short": "https://short.url/abc123"
  }
}
```

## 🙏 Acknowledgments

- [Jetpack Compose](https://developer.android.com/jetpack/compose) for the modern UI toolkit
- [Retrofit](https://square.github.io/retrofit/) for networking
- [Dagger Hilt](https://dagger.dev/hilt/) for dependency injection
- [Material Design](https://material.io/) for design guidelines
- [Detekt](https://detekt.dev/) for static code analysis
- [Detekt Compose Rules](https://mrmans0n.github.io/compose-rules/) for Compose-specific code quality
- [Article](https://phauer.com/2018/best-practices-unit-testing-kotlin/) Best Practices for Unit Testing in Kotlin

